/**
 * Real-time Firebase Listener Manager
 *
 * This service manages all real-time Firebase listeners for chats and comments,
 * providing centralized listener management, automatic cleanup, and enhanced
 * read receipt functionality.
 */

import {
    collection,
    query,
    orderBy,
    onSnapshot,
    where,
    doc,
    updateDoc,
    arrayUnion,
    serverTimestamp,
    getDoc
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import { getCollectionPaths, getChatIdentifier, determineChatType } from './collectionPaths';

/**
 * Real-time listener manager class
 */
class RealtimeManager {
    constructor() {
        this.listeners = new Map(); // Store active listeners
        this.chatListeners = new Map(); // Store chat-specific listeners
        this.commentListeners = new Map(); // Store comment-specific listeners
        this.readReceiptQueue = new Map(); // Queue for batching read receipts
        this.readReceiptTimer = null;
    }

    /**
     * Start listening to all chats for a specific page
     * @param {string} pageId - The page identifier
     * @param {Function} onChatsUpdate - Callback for chat updates
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToAllChats(pageId, onChatsUpdate, options = {}) {
        const listenerId = `all_chats_${pageId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            // Query all chats for the page using the correct path structure: pages/${pageId}/chats
            const chatsPath = `pages/${pageId}/chats`;
            const chatsQuery = query(
                collection(db, ...chatsPath.split('/')),
                orderBy('updated_time', 'desc')
            );

            console.log(`[REALTIME_DEBUG] Setting up chats listener for path: ${chatsPath}`);

            const unsubscribe = onSnapshot(chatsQuery, (snapshot) => {
                console.log(`[REALTIME_DEBUG] Chats snapshot received:`, {
                    size: snapshot.size,
                    empty: snapshot.empty,
                    hasPendingWrites: snapshot.metadata.hasPendingWrites,
                    fromCache: snapshot.metadata.fromCache
                });

                const chats = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    _lastUpdated: new Date().toISOString()
                }));

                console.log(`[REALTIME] Received ${chats.length} chat updates for page ${pageId}`);
                console.log(`[REALTIME_DEBUG] Chat data:`, chats);
                onChatsUpdate(chats);
            }, (error) => {
                console.error(`[REALTIME] Error in chats listener for page ${pageId}:`, error);
                onChatsUpdate([], error);
            });

            this.listeners.set(listenerId, unsubscribe);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up chats listener for page ${pageId}:`, error);
            onChatsUpdate([], error);
            return () => { };
        }
    }

    /**
     * Start listening to messages for a specific chat
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Function} onMessagesUpdate - Callback for message updates
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToMessages(chatId, chatType, pageId, onMessagesUpdate, options = {}) {
        console.log(`[REALTIME_DEBUG] *** listenToMessages called ***`, {
            chatId,
            chatType,
            pageId,
            options
        });

        const listenerId = `messages_${pageId}_${chatId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            // Use the same path structure as sendMessage: pages/${pageId}/chats/${senderId}/messages
            // chatId here is actually the sender ID from the Redux slice
            const messagesPath = `pages/${pageId}/chats/${chatId}/messages`;

            console.log(`[REALTIME_DEBUG] Messages listener setup:`, {
                chatId,
                chatType,
                pageId,
                messagesPath,
                fullPath: `${pageId}/${chatId}/messages`
            });

            // Compare with what getCollectionPaths would return
            console.log(`[REALTIME_DEBUG] *** PATH COMPARISON TEST ***`);
            console.log(`[REALTIME_DEBUG] Listener path: ${messagesPath}`);
            console.log(`[REALTIME_DEBUG] Expected format: pageId/senderId/messages`);
            console.log(`[REALTIME_DEBUG] Actual values: ${pageId}/${chatId}/messages`);

            console.log(`[REALTIME_DEBUG] Setting up messages query for path: ${messagesPath}`);

            // Test if the collection exists by doing a simple get first
            const testCollection = collection(db, ...messagesPath.split('/'));
            console.log(`[REALTIME_DEBUG] Testing collection existence:`, testCollection.path);

            // Do a one-time read to see if there are any documents
            import('firebase/firestore').then(({ getDocs }) => {
                getDocs(testCollection).then(snapshot => {
                    console.log(`[REALTIME_DEBUG] Collection test - found ${snapshot.size} documents`);
                    snapshot.forEach(doc => {
                        console.log(`[REALTIME_DEBUG] Existing document:`, doc.id, doc.data());
                    });
                }).catch(error => {
                    console.log(`[REALTIME_DEBUG] Error reading collection:`, error);
                });
            });

            const messagesQuery = query(
                testCollection,
                orderBy('created_time', 'asc')
            );

            console.log(`[REALTIME_DEBUG] About to set up onSnapshot listener...`);

            const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
                console.log(`[REALTIME_DEBUG] *** SNAPSHOT CALLBACK TRIGGERED ***`);
                // Debug logging for document changes
                console.log(`[REALTIME_DEBUG] Messages snapshot received for chat ${chatId}`);
                console.log(`[REALTIME_DEBUG] Snapshot metadata:`, {
                    hasPendingWrites: snapshot.metadata.hasPendingWrites,
                    fromCache: snapshot.metadata.fromCache,
                    size: snapshot.size,
                    empty: snapshot.empty
                });

                // Log document changes
                snapshot.docChanges().forEach((change) => {
                    console.log(`[REALTIME_DEBUG] Document ${change.type}:`, {
                        docId: change.doc.id,
                        data: change.doc.data(),
                        oldIndex: change.oldIndex,
                        newIndex: change.newIndex
                    });
                });

                const messages = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    _lastUpdated: new Date().toISOString()
                }));

                console.log(`[REALTIME] Received ${messages.length} message updates for chat ${chatId}`);
                console.log(`[REALTIME_DEBUG] Messages data:`, messages);

                onMessagesUpdate(messages);

                // Trigger unread count update for messages
                const event = new CustomEvent('messagesUpdated', {
                    detail: { messages, chatId, chatType, pageId }
                });
                window.dispatchEvent(event);
            }, (error) => {
                console.error(`[REALTIME_DEBUG] *** FIREBASE ERROR ***`, error);
                console.error(`[REALTIME] Error in messages listener for chat ${chatId}:`, error);
                onMessagesUpdate([], error);
            });

            console.log(`[REALTIME_DEBUG] onSnapshot listener set up successfully, unsubscribe function:`, typeof unsubscribe);

            this.listeners.set(listenerId, unsubscribe);
            this.chatListeners.set(chatId, listenerId);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up messages listener for chat ${chatId}:`, error);
            onMessagesUpdate([], error);
            return () => { };
        }
    }

    /**
     * Start listening to comments for a specific sender using the same path as messages
     * @param {string} senderId - The sender identifier (participant ID)
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Function} onCommentsUpdate - Callback for comment updates
     * @param {Object} currentUser - Current user information
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToComments(senderId, chatType, pageId, onCommentsUpdate, currentUser, options = {}) {
        const listenerId = `comments_${pageId}_${senderId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            // Use the same path structure as messages: pages/${pageId}/chats/${senderId}/comments
            const commentsPath = `pages/${pageId}/chats/${senderId}/comments`;

            const commentsQuery = query(
                collection(db, ...commentsPath.split('/')),
                orderBy('createdAt', 'asc')
            );

            const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
                const comments = snapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        ...data,
                        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                        readBy: data.readBy || [],
                        _lastUpdated: new Date().toISOString()
                    };
                });

                console.log(`[REALTIME] Received ${comments.length} comment updates for sender ${senderId}`);

                // Auto-mark new comments as read if user is actively viewing
                if (currentUser && options.autoMarkAsRead) {
                    this.queueReadReceipts(senderId, chatType, pageId, comments, currentUser);
                }

                onCommentsUpdate(comments);

                // Trigger scroll to bottom if specified in options
                if (options.autoScrollToBottom && comments.length > 0) {
                    // Small delay to ensure DOM is updated
                    setTimeout(() => {
                        const event = new CustomEvent('commentsUpdated', {
                            detail: { comments, senderId, scrollToBottom: true }
                        });
                        window.dispatchEvent(event);
                    }, 100);
                }

                // Additional check: Don't trigger notifications if comments modal is open
                // This provides an extra layer of protection against notification spam
                const isCommentsModalOpen = document.querySelector('.comments-modal') !== null;
                if (isCommentsModalOpen) {
                    console.log('[REALTIME] Comments modal is open - suppressing notifications');
                }
            }, (error) => {
                console.error(`[REALTIME] Error in comments listener for sender ${senderId}:`, error);
                onCommentsUpdate([], error);
            });

            this.listeners.set(listenerId, unsubscribe);
            this.commentListeners.set(senderId, listenerId);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up comments listener for sender ${senderId}:`, error);
            onCommentsUpdate([], error);
            return () => { };
        }
    }

    /**
     * Queue read receipts for batch processing
     * @param {string} senderId - The sender identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Array} comments - Array of comments
     * @param {Object} currentUser - Current user information
     */
    queueReadReceipts(senderId, chatType, pageId, comments, currentUser) {
        const unreadComments = comments.filter(comment => {
            // Check if current user has already read this comment
            return !comment.readBy?.some(receipt => receipt.userId === currentUser.id);
        });

        if (unreadComments.length === 0) return;

        const queueKey = `${pageId}_${senderId}`;
        this.readReceiptQueue.set(queueKey, {
            senderId,
            chatType,
            pageId,
            commentIds: unreadComments.map(c => c.id),
            user: currentUser,
            timestamp: Date.now()
        });

        // Debounce read receipt updates
        if (this.readReceiptTimer) {
            clearTimeout(this.readReceiptTimer);
        }

        this.readReceiptTimer = setTimeout(() => {
            this.processReadReceiptQueue();
        }, 1000); // Wait 1 second before processing
    }

    /**
     * Process queued read receipts
     */
    async processReadReceiptQueue() {
        const queue = Array.from(this.readReceiptQueue.values());
        this.readReceiptQueue.clear();

        for (const item of queue) {
            try {
                await this.markCommentsAsReadInFirebase(
                    item.senderId,
                    item.chatType,
                    item.pageId,
                    item.commentIds,
                    item.user
                );
            } catch (error) {
                console.error('[REALTIME] Error processing read receipt:', error);
            }
        }
    }

    /**
     * Mark comments as read in Firebase with real-time updates
     * @param {string} senderId - The sender identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Array} commentIds - Array of comment IDs
     * @param {Object} user - User information
     */
    async markCommentsAsReadInFirebase(senderId, chatType, pageId, commentIds, user) {
        try {
            // Use the same path structure as messages: pages/${pageId}/chats/${senderId}/comments
            const commentsPath = `pages/${pageId}/chats/${senderId}/comments`;

            const readReceipt = {
                userId: user.id,
                userName: user.name,
                userPhoto: user.photo || null,
                readAt: new Date().toISOString()
            };

            // Update each comment with read receipt
            const updatePromises = commentIds.map(async (commentId) => {
                const commentRef = doc(db, ...commentsPath.split('/'), commentId);

                // Check if user already has a read receipt
                const commentDoc = await getDoc(commentRef);
                if (commentDoc.exists()) {
                    const currentData = commentDoc.data();
                    const existingReadBy = currentData.readBy || [];

                    const userAlreadyRead = existingReadBy.some(receipt =>
                        receipt.userId === user.id
                    );

                    if (!userAlreadyRead) {
                        await updateDoc(commentRef, {
                            readBy: arrayUnion(readReceipt),
                            lastReadUpdate: serverTimestamp()
                        });
                        console.log(`[REALTIME] Marked comment ${commentId} as read by ${user.name}`);
                    }
                }
            });

            await Promise.all(updatePromises);
        } catch (error) {
            console.error('[REALTIME] Error marking comments as read:', error);
            throw error;
        }
    }

    /**
     * Clean up a specific listener
     * @param {string} listenerId - The listener identifier
     */
    cleanup(listenerId) {
        const unsubscribe = this.listeners.get(listenerId);
        if (unsubscribe) {
            try {
                unsubscribe();
                console.log(`[REALTIME] Cleaned up listener: ${listenerId}`);
            } catch (error) {
                console.warn(`[REALTIME] Error cleaning up listener ${listenerId}:`, error);
            }
            this.listeners.delete(listenerId);
        }
    }

    /**
     * Clean up all listeners
     */
    cleanupAll() {
        console.log(`[REALTIME] Cleaning up ${this.listeners.size} listeners`);

        for (const [listenerId, unsubscribe] of this.listeners) {
            try {
                unsubscribe();
            } catch (error) {
                console.warn(`[REALTIME] Error cleaning up listener ${listenerId}:`, error);
            }
        }

        this.listeners.clear();
        this.chatListeners.clear();
        this.commentListeners.clear();
        this.readReceiptQueue.clear();

        if (this.readReceiptTimer) {
            clearTimeout(this.readReceiptTimer);
            this.readReceiptTimer = null;
        }
    }

    /**
     * Get active listener count
     * @returns {number} - Number of active listeners
     */
    getActiveListenerCount() {
        return this.listeners.size;
    }

    /**
     * Get listener status
     * @returns {Object} - Listener status information
     */
    getStatus() {
        return {
            totalListeners: this.listeners.size,
            chatListeners: this.chatListeners.size,
            commentListeners: this.commentListeners.size,
            queuedReadReceipts: this.readReceiptQueue.size,
            activeListeners: Array.from(this.listeners.keys())
        };
    }
}

// Create singleton instance
const realtimeManager = new RealtimeManager();

export default realtimeManager;

// Export individual methods for convenience
export const {
    listenToAllChats,
    listenToMessages,
    listenToComments,
    markCommentsAsReadInFirebase,
    cleanup,
    cleanupAll,
    getActiveListenerCount,
    getStatus
} = realtimeManager;

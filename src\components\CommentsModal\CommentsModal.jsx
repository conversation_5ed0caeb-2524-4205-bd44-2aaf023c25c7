import React, { useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FaTimes } from "react-icons/fa";
import {
  selectCommentsModalOpen,
  selectSelectedChat,
  selectSelectedWhatsappChat,
  selectActiveFilter,
  selectSelectedPage,
  selectComments,
  selectCommentsLoading,
  selectCommentsInitialLoading,
  closeCommentsModal,
  loadChatCommentsHybrid,
  markCommentsAsReadHybrid,
  resetCommentsUnreadCount,
  stopCommentsListener,
} from "../../redux/features/metaBusinessChatSlice";
import Comment from "../Comment/Comment";
import CommentInput from "../CommentInput/CommentInput";
import { extractUserInfo, getUnreadComments } from "../../utils/chatUtils";
import { getChatIds, clearFirebaseComments } from "../../services/comments/hybridService";
import { useRealtimeChat } from "../../hooks/useRealtimeChat";
import "./CommentsModal.css";

const CommentsModal = () => {
  const dispatch = useDispatch();
  const isOpen = useSelector(selectCommentsModalOpen);
  const selectedChat = useSelector(selectSelectedChat);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);
  const activeFilter = useSelector(selectActiveFilter);
  const selectedPage = useSelector(selectSelectedPage);
  const comments = useSelector(selectComments);
  const commentsLoading = useSelector(selectCommentsLoading);
  const commentsInitialLoading = useSelector(selectCommentsInitialLoading);
  const currentUser = useSelector((state) => state.auth.user);

  const modalRef = useRef(null);
  const backdropRef = useRef(null);
  const commentsContainerRef = useRef(null);
  const hasMarkedAsReadRef = useRef(false);

  // Use the real-time chat hook for enhanced functionality
  const {
    startCommentsListener,
    markCommentsAsRead,
    stopAllListeners,
    isCommentsListenerActive,
    getListenerStatus,
  } = useRealtimeChat({
    autoStartChatsListener: false, // Don't auto-start chats listener in modal
    autoStartMessagesListener: false, // Don't auto-start messages listener in modal
    autoStartCommentsListener: true, // Auto-start comments listener when modal opens
    autoMarkCommentsAsRead: true, // Automatically mark comments as read when viewed
    cleanupOnUnmount: false, // Don't cleanup all listeners when modal unmounts
  });

  // Get chat title and subtitle based on active filter
  const getChatInfo = () => {
    if (activeFilter === "whatsapp" && selectedWhatsappChat) {
      return {
        title: selectedWhatsappChat.sender_name || "WhatsApp User",
        subtitle: "WhatsApp Lead Discussion",
      };
    } else if (selectedChat) {
      if (selectedChat.flage === "instagram") {
        return {
          title:
            selectedChat.participants?.data[1]?.username || "Instagram User",
          subtitle: "Instagram Lead Discussion",
        };
      } else {
        return {
          title: selectedChat.participants?.data[0]?.name || "Messenger User",
          subtitle: "Messenger Lead Discussion",
        };
      }
    }
    return {
      title: "Chat Comments",
      subtitle: "Team Discussion",
    };
  };

  // Handle modal close
  const handleClose = () => {
    dispatch(closeCommentsModal());
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === backdropRef.current) {
      handleClose();
    }
  };

  // Scroll to bottom function
  const scrollToBottom = (smooth = true) => {
    if (commentsContainerRef.current) {
      const scrollContainer = commentsContainerRef.current;
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    }
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      if (e.key === "Escape") {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  // Load comments when modal opens (hybrid: backend + Firebase real-time)
  useEffect(() => {
    if (isOpen && currentUser) {
      const chatIds = getChatIds(
        selectedChat,
        selectedWhatsappChat,
        activeFilter
      );

      if (chatIds) {
        // Clear Firebase comments first (like messages do)
        clearFirebaseComments(
          chatIds.firebaseSenderId,
          chatIds.chatType,
          selectedPage?.page_id
        );

        // Load comments using hybrid approach:
        // - Backend API for history using chat ID
        // - Firebase for real-time updates using sender ID path
        dispatch(
          loadChatCommentsHybrid({
            backendChatId: chatIds.backendChatId,
            firebaseChatId: chatIds.firebaseSenderId, // Use sender ID for Firebase path
            chatType: chatIds.chatType,
          })
        );

        // Start real-time comments listener for live updates
        startCommentsListener();

        console.log(
          `[COMMENTS_MODAL] Loading comments - Backend: ${chatIds.backendChatId}, Firebase: ${chatIds.firebaseSenderId}, Real-time: ${isCommentsListenerActive}`
        );
      }
    } else if (!isOpen) {
      // Stop listener when modal closes
      dispatch(stopCommentsListener());

      // Reset the read flag for next time
      hasMarkedAsReadRef.current = false;
    }

    // Cleanup on unmount
    return () => {
      if (!isOpen) {
        dispatch(stopCommentsListener());
      }
    };
  }, [
    isOpen,
    currentUser,
    selectedChat,
    selectedWhatsappChat,
    activeFilter,
    dispatch,
    startCommentsListener,
    isCommentsListenerActive,
  ]);

  // Removed refresh logic to prevent infinite loops
  // Real-time sync should handle read receipt updates automatically

  // Enhanced read receipt handling (hybrid: backend + Firebase real-time)
  useEffect(() => {
    if (
      isOpen &&
      comments.length > 0 &&
      currentUser &&
      !commentsLoading &&
      !hasMarkedAsReadRef.current
    ) {
      // Add a small delay to ensure comments are fully processed
      const timer = setTimeout(() => {
        const userInfo = extractUserInfo(currentUser);

        if (userInfo) {
          const unreadComments = getUnreadComments(comments, userInfo.id);
          const unreadCommentIds = unreadComments.map((comment) => comment.id);

          if (unreadCommentIds.length > 0) {
            console.log(
              `[COMMENTS_MODAL] Marking ${unreadCommentIds.length} comments as read using hybrid approach`
            );

            // Use hybrid approach for marking comments as read
            const chatIds = getChatIds(
              selectedChat,
              selectedWhatsappChat,
              activeFilter
            );

            if (chatIds) {
              dispatch(
                markCommentsAsReadHybrid({
                  backendChatId: chatIds.backendChatId,
                  firebaseChatId: chatIds.firebaseSenderId, // Use sender ID for Firebase path
                  chatType: chatIds.chatType,
                  commentIds: unreadCommentIds,
                  user: userInfo,
                })
              );
            }

            // Reset unread count in UI
            dispatch(resetCommentsUnreadCount());

            // Mark that we've already processed read receipts for this session
            hasMarkedAsReadRef.current = true;
          }
        }
      }, 100); // Small delay to ensure comments are processed

      return () => clearTimeout(timer);
    }
  }, [
    isOpen,
    comments,
    currentUser,
    commentsLoading,
    selectedChat,
    selectedWhatsappChat,
    activeFilter,
    dispatch,
  ]);

  // Reset the hasMarkedAsRead flag when modal closes
  useEffect(() => {
    if (!isOpen) {
      hasMarkedAsReadRef.current = false;
    }
  }, [isOpen]);

  // Scroll to bottom when comments are loaded initially
  useEffect(() => {
    if (isOpen && comments.length > 0 && !commentsInitialLoading) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom(false); // Instant scroll for initial load
      }, 100);
    }
  }, [isOpen, comments.length, commentsInitialLoading]);

  // Disabled: Don't scroll to bottom when new comments are added (real-time updates)
  // This was causing unwanted scrolling when users were reading older comments
  // useEffect(() => {
  //   if (isOpen && comments.length > 0) {
  //     const lastComment = comments[comments.length - 1];
  //     const currentUserId = currentUser?.user?.id || currentUser?.id;

  //     // If the last comment is from the current user, scroll immediately
  //     // If it's from someone else, scroll smoothly
  //     if (lastComment && lastComment.author?.id === currentUserId) {
  //       setTimeout(() => {
  //         scrollToBottom(true); // Smooth scroll for user's own comments
  //       }, 100);
  //     } else if (lastComment) {
  //       // For other users' comments, scroll smoothly after a short delay
  //       setTimeout(() => {
  //         scrollToBottom(true);
  //       }, 300);
  //     }
  //   }
  // }, [comments, isOpen, currentUser]);

  if (!isOpen) return null;

  return (
    <div
      ref={backdropRef}
      className="comments-modal-backdrop"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="comments-modal-container"
        tabIndex={-1}
        role="dialog"
        aria-labelledby="comments-modal-title"
        aria-modal="true"
      >
        <div className="comments-modal-content">
          {/* Modal Header */}
          <div className="comments-modal-header">
            <div>
              <h5 id="comments-modal-title" className="comments-modal-title">
                {getChatInfo().title}
              </h5>
              <p className="comments-modal-subtitle">
                {getChatInfo().subtitle}
                {/* Real-time status indicator */}
                {isCommentsListenerActive && (
                  <span
                    className="realtime-status-indicator"
                    title="Real-time updates active"
                    style={{
                      marginLeft: "8px",
                      color: "#10b981",
                      fontSize: "12px",
                      fontWeight: "bold",
                    }}
                  >
                    ● LIVE
                  </span>
                )}
              </p>
            </div>
            <button
              type="button"
              className="comments-modal-close-btn"
              onClick={handleClose}
              aria-label="Close comments modal"
            >
              <FaTimes />
            </button>
          </div>

          {/* Modal Body */}
          <div className="comments-modal-body">
            {/* Comments Container - Scrollable */}
            <div className="comments-container" ref={commentsContainerRef}>
              {commentsInitialLoading ? (
                <div className="comments-loading-skeleton">
                  {/* Skeleton loading animation */}
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="comment-skeleton">
                      <div className="comment-skeleton-avatar"></div>
                      <div className="comment-skeleton-content">
                        <div className="comment-skeleton-header">
                          <div className="comment-skeleton-name"></div>
                          <div className="comment-skeleton-time"></div>
                        </div>
                        <div className="comment-skeleton-text"></div>
                        <div
                          className={`comment-skeleton-text ${
                            index % 2 === 0 ? "short" : ""
                          }`}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : comments.length > 0 ? (
                <div className="comments-list">
                  {comments.map((comment) => (
                    <Comment
                      key={comment.id}
                      comment={comment}
                      showReadReceipts={true}
                      currentUser={currentUser}
                    />
                  ))}
                </div>
              ) : (
                <div className="comments-placeholder">
                  <div className="empty-state-icon">💬</div>
                  <p className="empty-state-title">Start the conversation</p>
                  <p className="empty-state-subtitle">
                    Share thoughts and collaborate with your team about this
                    lead
                  </p>
                </div>
              )}
            </div>

            {/* Comment Input - Fixed at bottom */}
            <div className="comment-input-container">
              <CommentInput
                placeholder="Add a comment..."
                disabled={commentsLoading} // Only disabled during comment submission, not initial loading
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommentsModal;

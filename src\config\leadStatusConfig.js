/**
 * Lead Status Configuration
 *
 * This module manages lead status definitions and visibility based on user roles.
 * Special admin users (defined in packageVisibility.js) will see an extended set of statuses.
 */

import { isUserExcluded } from './packageVisibility';

// Basic lead statuses (visible to all users)
const BASIC_LEAD_STATUSES = {
    0: 'Pending',
    1: 'In Progress',
    2: 'Completed',
    3: 'Rejected',
    4: 'Wrong Lead',
    5: 'Not Qualified',
    6: 'No Communication',
    7: 'Booked',
    8: 'Booked and Reserved',
    9: 'Canceled',
    10: 'Quotation',
    11: 'Assigned'
};

// Extended lead statuses (only visible to special admin users)
const EXTENDED_LEAD_STATUSES = {
    ...BASIC_LEAD_STATUSES,
    12: 'Undefined',
    13: 'Advance Paid',
    14: 'Follow Up',
    15: 'Not Interested',
    16: 'Junk',
    17: 'Complaints',
    18: 'Urgent Call',
    19: 'Call Back',
    20: 'Booked ACTION 2',
    21: 'Sent SMS',
    22: 'No Action',
    23: 'Not interested ACTION 2',
    24: 'Whatapp-NAL',
    25: 'Follow up ACTION 2',
    26: 'Complain ACTION 2'
};

// Statuses that should always be visible regardless of user role
const ALWAYS_VISIBLE_STATUSES = [0, 11]; // Pending and Assigned

/**
 * Get the appropriate lead statuses based on user role
 * @param {number|string} userId - The user ID to check
 * @returns {Object} - Object mapping status codes to status names
 */
export const getLeadStatuses = (userId) => {
    // Special admin users get the extended set of statuses
    if (isUserExcluded(userId)) {
        return EXTENDED_LEAD_STATUSES;
    }

    // Regular users get the basic set of statuses
    return BASIC_LEAD_STATUSES;
};

/**
 * Get the visible status options for a user (for dropdowns, filters, etc.)
 * @param {number|string} userId - The user ID to check
 * @returns {Object} - Object mapping status codes to status names
 */
export const getVisibleStatusOptions = (userId) => {
    // For special admin users, only show new statuses and always visible ones
    if (isUserExcluded(userId)) {
        const visibleStatuses = {};

        // Add always visible statuses
        ALWAYS_VISIBLE_STATUSES.forEach(statusCode => {
            visibleStatuses[statusCode] = BASIC_LEAD_STATUSES[statusCode];
        });

        // Add extended statuses (12-26)
        Object.entries(EXTENDED_LEAD_STATUSES).forEach(([code, name]) => {
            if (parseInt(code) >= 12) {
                visibleStatuses[code] = name;
            }
        });

        return visibleStatuses;
    }

    // Regular users get all basic statuses
    return BASIC_LEAD_STATUSES;
};

/**
 * Check if a status should be visible to a specific user
 * @param {number|string} userId - The user ID to check
 * @param {number|string} statusCode - The status code to check
 * @returns {boolean} - True if the status should be visible to the user
 */
export const isStatusVisibleToUser = (userId, statusCode) => {
    const numericStatusCode = parseInt(statusCode);

    // For special admin users
    if (isUserExcluded(userId)) {
        // Always show statuses 0 and 11 (Pending and Assigned)
        if (ALWAYS_VISIBLE_STATUSES.includes(numericStatusCode)) {
            return true;
        }

        // Show extended statuses (12-26)
        return numericStatusCode >= 12 && numericStatusCode <= 26;
    }

    // For regular users, show all basic statuses
    return numericStatusCode in BASIC_LEAD_STATUSES;
};

/**
 * Get all lead statuses (for reference purposes)
 * @returns {Object} - Object containing all defined lead statuses
 */
export const getAllLeadStatuses = () => {
    return EXTENDED_LEAD_STATUSES;
};

// Re-export isUserExcluded for convenience
export { isUserExcluded };

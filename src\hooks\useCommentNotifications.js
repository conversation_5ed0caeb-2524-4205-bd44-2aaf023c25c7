import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
    selectComments,
    updateUnreadCommentCounts,
    selectShouldPlayNotificationSound,
    resetNotificationSound
} from '../redux/features/metaBusinessChatSlice';
import { playNotificationSound } from '../utils/notificationSound';

/**
 * Hook to handle comment notifications and unread counts
 */
export const useCommentNotifications = () => {
    const dispatch = useDispatch();
    const comments = useSelector(selectComments);
    const shouldPlaySound = useSelector(selectShouldPlayNotificationSound);
    const currentUser = useSelector((state) => state.auth.user);

    // Update unread counts when comments change
    useEffect(() => {
        if (currentUser?.user?.id) {
            dispatch(updateUnreadCommentCounts({
                currentUserId: String(currentUser.user.id)
            }));
        }
    }, [comments, currentUser, dispatch]);

    // Handle notification sound
    useEffect(() => {
        if (shouldPlaySound) {
            playNotificationSound();
            dispatch(resetNotificationSound());
        }
    }, [shouldPlaySound, dispatch]);
};

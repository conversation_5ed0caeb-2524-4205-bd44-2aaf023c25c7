import { useFormik } from "formik";
import Form from "react-bootstrap/Form";
import * as yup from "yup";
import { useEffect } from "react";
import { Button } from "react-bootstrap";
import leadService from "../../services/leads";
import { toast } from "react-toastify";
import QuotationOfferFile from "./QuotationOfferFile";
import { quotationStatus } from "./ActivitiesTabsData.module";
import * as Yup from "yup";
import { safeNumber } from "../../utils/safe-number";
import { FaWhatsapp } from "react-icons/fa6";
import { FaPhone } from "react-icons/fa";
import { BiLogoGmail } from "react-icons/bi";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import {
  getLeadStatuses,
  getAllLeadStatuses,
} from "../../config/leadStatusConfig";
import { isUserExcluded } from "../../config/packageVisibility";

// Define status code constants to avoid hardcoding
const LEAD_STATUS = {
  BOOKED_AND_RESERVED: 8,
  QUOTATION: 10,
};

// Define quotation status code constants
const QUOTATION_STATUS = {
  PENDING: 0,
  ACCEPTED: 1,
  REJECTED: 2,
};

const EditLeadForm = ({ leadDetails, setLeadDetails }) => {
  const { t } = useTranslation();
  const { user } = useSelector((state) => state.auth);

  const handleEditLeadSubmit = async (values) => {
    const parsedStatus = safeNumber(values.status);
    values.status = parsedStatus;
    const hasChanges =
      values.email !== leadDetails?.email ||
      values.name !== leadDetails?.name ||
      values.phone !== leadDetails?.phone ||
      values.amount !== leadDetails?.amount ||
      values.service !== leadDetails?.service ||
      parsedStatus !== safeNumber(leadDetails?.status);
    if (!hasChanges) {
      toast.warn(t("forms.messages.noChanges", "No changes detected"), {
        position: "bottom-right",
        theme: "dark",
      });
      return;
    }
    try {
      // Remove amount field if status is not BOOKED_AND_RESERVED
      if (safeNumber(values.status) !== LEAD_STATUS.BOOKED_AND_RESERVED) {
        delete values.amount;
      }

      // Remove quotation-related fields if status is not QUOTATION
      if (safeNumber(values.status) !== LEAD_STATUS.QUOTATION) {
        delete values.quotation_amount;
        delete values.quotation_status;
        delete values.quotation_offer;
        delete values.refuse_reason;
      }

      if (values.refuse_reason === undefined || values.refuse_reason === null) {
        delete values.refuse_reason;
      }
      if (values.action_proven === null) {
        delete values.action_proven;
      }

      const response = await leadService.updateSingleLeadApi(leadDetails?.id, {
        ...values,
        quotation_status: safeNumber(values.quotation_status),
      });
      if (response && response.status === 200 && response.data) {
        toast.success(
          response.message ||
            t("forms.messages.leadUpdated", "Lead updated successfully"),
          { position: "bottom-right", theme: "dark" }
        );
        setLeadDetails(response?.data);
        formik.resetForm({
          ...formik.values,
          status: null,
        });
      } else {
        toast.error(
          response.message ||
            t("forms.messages.updateError", "Error updating the lead"),
          { position: "bottom-right", theme: "dark" }
        );
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
          t("forms.messages.updateError", "Error updating the lead"),
        { position: "bottom-right", theme: "dark" }
      );
    }
  };

  const validationSchema = yup.object().shape({
    name: yup
      .string()
      .required(
        t("forms.validation.nameRequired", "Please enter the lead's name")
      ),
    email: yup
      .string()
      .email(t("forms.validation.validEmail", "Please enter a valid email")),
    status: yup
      .string()
      .required(
        t("forms.validation.statusRequired", "Please select the lead's status")
      ),
    amount: yup
      .string()
      .test(
        "requiredForBookedAndReserved",
        t("forms.validation.amountRequired", "Amount is required"),
        function (value) {
          if (
            safeNumber(this.parent.status) === LEAD_STATUS.BOOKED_AND_RESERVED
          ) {
            return !!value;
          }
          return true;
        }
      ),
    quotation_amount: Yup.string().test(
      "requiredForQuotation",
      t(
        "forms.validation.quotationAmountRequired",
        "Quotation amount is required"
      ),
      function (value) {
        if (safeNumber(this.parent.status) === LEAD_STATUS.QUOTATION) {
          return !!value;
        }
        return true;
      }
    ),
    quotation_status: Yup.string()
      .test(
        "requiredForQuotation",
        t(
          "forms.validation.quotationStatusRequired",
          "Quotation status is required"
        ),
        function (value) {
          if (safeNumber(this.parent.status) === LEAD_STATUS.QUOTATION) {
            return !!value;
          }
          return true;
        }
      )
      .nullable(),
    refuse_reason: Yup.string().test(
      "requiredForRejectedQuotation",
      t("forms.validation.refuseReasonRequired", "Refuse reason is required"),
      function (value) {
        if (
          safeNumber(this.parent.quotation_status) === QUOTATION_STATUS.REJECTED
        ) {
          return !!value;
        }
        return true;
      }
    ),
    quotation_offer: Yup.mixed()
      .test(
        "requiredForQuotation",
        t(
          "forms.validation.quotationOfferRequired",
          "Quotation offer is required"
        ),
        function (value) {
          if (safeNumber(this.parent.status) === LEAD_STATUS.QUOTATION) {
            return value && value instanceof File;
          }
          return true;
        }
      )
      .nullable(),
  });

  const formik = useFormik({
    initialValues: {
      name: leadDetails?.name || "",
      email: leadDetails?.email || "",
      phone: leadDetails?.phone || "",
      status: leadDetails?.status || "",
      amount: leadDetails?.amount || "",
      service: leadDetails?.service || "",
      quotation_amount: "",
      quotation_status: null,
      quotation_offer: null,
      refuse_reason: "",
    },
    onSubmit: handleEditLeadSubmit,
    validationSchema: validationSchema,
  });

  useEffect(() => {
    const isValidEmail = (email) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    formik.setValues({
      name: leadDetails?.name || "",
      email: isValidEmail(leadDetails?.email) ? leadDetails?.email : "",
      phone: leadDetails?.phone || "",
      status:
        leadDetails?.status === LEAD_STATUS.PENDING
          ? "0"
          : leadDetails?.status || "",
      amount: leadDetails?.amount || "",
      service: leadDetails?.service || "",
      quotation_amount: "",
      quotation_status: null,
      quotation_offer: null,
      refuse_reason: "",
    });
  }, [leadDetails]);

  // console.log(navigator.userAgent)
  // const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

  return (
    <>
      <Form noValidate onSubmit={formik.handleSubmit} autoComplete="off">
        <Form.Group className="mb-3">
          <Form.Label>{t("forms.name")}</Form.Label>
          <Form.Control
            name="name"
            type="text"
            placeholder={t("forms.placeholder.name")}
            value={formik.values.name}
            onChange={formik.handleChange}
            isValid={formik.touched.name && !formik.errors.name}
            isInvalid={formik.touched.name && formik.errors.name}
            autoComplete="off"
            data-form-type="other"
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors.name}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group className="mb-3 position-relative">
          <Form.Label>{t("forms.emailAddress")}</Form.Label>
          <div className="d-flex">
            <Form.Control
              name="email"
              type="email"
              placeholder={t("forms.placeholder.email")}
              value={formik.values.email}
              onChange={formik.handleChange}
              isValid={formik.touched.email && !formik.errors.email}
              isInvalid={formik.touched.email && formik.errors.email}
              autoComplete="off"
              data-form-type="other"
            />
            {formik.values.email && (
              <a
                href={`mailto:${formik.values.email}`}
                className="ms-2 mail-icon"
                onClick={(e) => {
                  if (!formik.values.email || formik.errors.email) {
                    e.preventDefault(); // Prevent opening mail if email is invalid
                  }
                }}
              >
                <BiLogoGmail className={"mainColor"} size={24} />
              </a>
            )}
          </div>
          <Form.Control.Feedback type="invalid">
            {formik.errors.email}
          </Form.Control.Feedback>
        </Form.Group>

        <Form.Group className="mb-3 position-relative">
          <Form.Label>{t("forms.phoneNumber")}</Form.Label>
          <Form.Control
            name="phone"
            type="text"
            placeholder={t("forms.placeholder.phone")}
            value={formik.values.phone}
            onChange={formik.handleChange}
            isValid={formik.touched.phone && !formik.errors.phone}
            isInvalid={formik.touched.phone && formik.errors.phone}
          />
          {formik.values.phone && (
            <div className={"d-flex justify-content-end call-icons"}>
              <a
                href={`https://wa.me/${formik.values.phone}`}
                target="_blank"
                rel="noopener noreferrer"
                className="ms-2"
                onClick={(e) => {
                  const isMobile = /iPhone|iPad|iPod|Android/i.test(
                    navigator.userAgent
                  );

                  if (!isMobile) {
                    e.preventDefault(); // Prevent the default `wa.me` link for desktop users
                    // Try opening WhatsApp Desktop app
                    window.location.href = `whatsapp://send?phone=${formik.values.phone}`;
                    // Fallback to WhatsApp Web after a delay
                    setTimeout(() => {
                      window.open(
                        `https://web.whatsapp.com/send?phone=${formik.values.phone}`,
                        "_blank"
                      );
                    }, 1000); // Give it time to open the app before trying WhatsApp Web
                  }
                }}
                disabled={!formik.values.phone || formik.errors.phone}
              >
                <FaWhatsapp color={"rgb(41, 167, 26)"} size={24} />
              </a>

              <a
                href={`tel:${formik.values.phone}`}
                className="ms-2"
                disabled={!formik.values.phone || formik.errors.phone}
              >
                <FaPhone color={"rgb(0, 132, 255)"} size={24} />
              </a>
            </div>
          )}

          <Form.Control.Feedback type="invalid">
            {formik.errors.phone}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group className="mb-4">
          <Form.Label>{t("forms.status")}</Form.Label>
          <Form.Select
            name="status"
            aria-label={t("forms.status")}
            value={formik.values.status}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isValid={formik.touched.status && !formik.errors.status}
            isInvalid={formik.touched.status && formik.errors.status}
          >
            <option value="">{t("forms.selectStatus")}</option>
            {Object.entries(getLeadStatuses(user?.user?.id)).map(
              ([statusCode, statusName]) => (
                <option key={statusCode} value={statusCode}>
                  {t(
                    `status.${statusName.toLowerCase().replace(/\s+/g, "")}`,
                    statusName
                  )}
                </option>
              )
            )}
          </Form.Select>
          <Form.Control.Feedback type="invalid">
            {formik.errors.status}
          </Form.Control.Feedback>
        </Form.Group>
        {safeNumber(formik.values.status) === LEAD_STATUS.QUOTATION ? (
          <>
            <Form.Group className={"mb-3"}>
              <Form.Label>{t("forms.quotationAmount")}</Form.Label>
              <Form.Control
                type="number"
                placeholder={t("forms.placeholder.amount")}
                value={formik.values.quotation_amount}
                onChange={formik.handleChange}
                isValid={
                  formik.touched.quotation_amount &&
                  !formik.errors.quotation_amount
                }
                isInvalid={
                  formik.touched.quotation_amount &&
                  formik.errors.quotation_amount
                }
                name="quotation_amount"
              />
              <Form.Control.Feedback type="invalid">
                {formik.errors.quotation_amount}
              </Form.Control.Feedback>
            </Form.Group>
            <Form.Group className={"mb-3"}>
              <Form.Label>{t("forms.uploadQuotation")}</Form.Label>
              <QuotationOfferFile
                setQuotationOffer={(file) =>
                  formik.setFieldValue("quotation_offer", file)
                }
              />
              <Form.Control
                type="file"
                hidden
                isValid={
                  formik.touched.quotation_offer &&
                  !formik.errors.quotation_offer
                }
                isInvalid={
                  formik.touched.quotation_offer &&
                  formik.errors.quotation_offer
                }
                name="quotation_offer"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    formik.setFieldValue("quotation_offer", file);
                  }
                }}
              />
              <Form.Control.Feedback type="invalid">
                {formik.errors.quotation_offer}
              </Form.Control.Feedback>
            </Form.Group>
            <Form.Group className={"mb-3"}>
              <Form.Label>{t("forms.selectQuotationStatus")}:</Form.Label>
              <Form.Select
                value={formik.values.quotation_status || ""}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                isValid={
                  formik.touched.quotation_status &&
                  !formik.errors.quotation_status
                }
                isInvalid={
                  formik.touched.quotation_status &&
                  formik.errors.quotation_status
                }
                name="quotation_status"
              >
                <option value="" label={t("forms.selectQuotationStatus")} />
                {quotationStatus.map((option) => (
                  <option key={option.value} value={option.value}>
                    {t(
                      `quotationStatus.${option.label.toLowerCase()}`,
                      option.label
                    )}
                  </option>
                ))}
              </Form.Select>
              <Form.Control.Feedback type="invalid">
                {formik.errors.quotation_status}
              </Form.Control.Feedback>
            </Form.Group>
            {safeNumber(leadDetails?.quotation_status) ===
              QUOTATION_STATUS.REJECTED ||
            safeNumber(formik.values.quotation_status) ===
              QUOTATION_STATUS.REJECTED ? (
              <Form.Group className={"mb-3"}>
                <Form.Label>{t("forms.refuseReason")}</Form.Label>
                <Form.Control
                  as="textarea"
                  placeholder={t("forms.addRejectionReason")}
                  style={{ height: "100px" }}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.refuse_reason}
                  isValid={
                    formik.touched.refuse_reason && !formik.errors.refuse_reason
                  }
                  isInvalid={
                    formik.touched.refuse_reason && formik.errors.refuse_reason
                  }
                  name="refuse_reason"
                />
                <Form.Control.Feedback type="invalid">
                  {formik.errors.refuse_reason}
                </Form.Control.Feedback>
              </Form.Group>
            ) : null}
          </>
        ) : null}
        {safeNumber(formik.values.status) ===
        LEAD_STATUS.BOOKED_AND_RESERVED ? (
          <Form.Group className="mb-3">
            <Form.Label>{t("forms.amount")}</Form.Label>
            <Form.Control
              name="amount"
              type="number"
              placeholder={t("forms.amount")}
              value={formik.values.amount}
              onChange={formik.handleChange}
              isValid={formik.touched.amount && !formik.errors.amount}
              isInvalid={formik.touched.amount && formik.errors.amount}
            />
            <Form.Control.Feedback type="invalid">
              {formik.errors.amount}
            </Form.Control.Feedback>
          </Form.Group>
        ) : null}
        <Form.Group className="mb-3">
          <Form.Label>{t("forms.service")}</Form.Label>
          {isUserExcluded(user?.user?.id) ? (
            <Form.Select
              name="service"
              value={formik.values.service}
              onChange={formik.handleChange}
              isValid={formik.touched.service && !formik.errors.service}
              isInvalid={formik.touched.service && formik.errors.service}
            >
              <option value="">{t("forms.selectService")}</option>
              <option value="Laser">Laser</option>
              <option value="Hollywood Smile">Hollywood Smile</option>
              <option value="Derma">Derma</option>
              <option value="Skin Care">Skin Care</option>
              <option value="Dental Implant">Dental Implant</option>
              <option value="Dental">Dental</option>
              <option value="Orthodontics">Orthodontics</option>
              <option value="Slimming">Slimming</option>
              <option value="Gyne">Gyne</option>
            </Form.Select>
          ) : (
            <Form.Control
              name="service"
              type="text"
              placeholder={t("forms.service")}
              value={formik.values.service}
              onChange={formik.handleChange}
              isValid={formik.touched.service && !formik.errors.service}
              isInvalid={formik.touched.service && formik.errors.service}
            />
          )}
          <Form.Control.Feedback type="invalid">
            {formik.errors.service}
          </Form.Control.Feedback>
        </Form.Group>
        <center>
          <Button type="submit" className="submit-btn">
            Submit
          </Button>
        </center>
      </Form>
    </>
  );
};

export default EditLeadForm;

import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    teamMembersFilters: {
        startDate: null,
        endDate: null,
        selectedTM: null,
        role: null,
        dateRange: 'custom',
    },
    salesFilters: {
    startDate: null,
    endDate: null,
    selectedTM: null,
    status: null,
    dateRange: 'custom',
  },
    leadAssignmentFilters: {
        startDate: null,
        endDate: null,
        selectedTM: null,
        source: null,
        status: null,
        dateRange: 'custom',
    },
    departmentFilters: {
        startDate: null,
        endDate: null,
        dateRange: 'custom',
        reportType: 'sources',
    },
};

const reportsSlice = createSlice({
    name: 'reports',
    initialState,
    reducers: {
        setTeamMembersFilters(state, action) {
            state.teamMembersFilters = { ...state.teamMembersFilters, ...action.payload };
        },
        clearTeamMembersFilters(state) {
            state.teamMembersFilters = { ...initialState.teamMembersFilters };
        },
        setSalesFilters(state, action) {
            state.salesFilters = { ...state.salesFilters, ...action.payload };
        },
        clearSalesFilters(state) {
            state.salesFilters = { ...initialState.salesFilters };
        },
        setLeadAssignmentFilters(state, action) {
            state.leadAssignmentFilters = { ...state.leadAssignmentFilters, ...action.payload };
        },
        clearLeadAssignmentFilters(state) {
            state.leadAssignmentFilters = { ...initialState.leadAssignmentFilters };
        },
        setDepartmentFilters(state, action) {
            state.departmentFilters = { ...state.departmentFilters, ...action.payload };
        },
        clearDepartmentFilters(state) {
            state.departmentFilters = { ...initialState.departmentFilters };
        },
    },
});

export const {
    setTeamMembersFilters,
    clearTeamMembersFilters,
    setSalesFilters,
    clearSalesFilters,
    setLeadAssignmentFilters,
    clearLeadAssignmentFilters,
    setDepartmentFilters,
    clearDepartmentFilters,
} = reportsSlice.actions;

export default reportsSlice.reducer;

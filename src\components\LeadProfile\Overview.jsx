import { <PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import { RxAct<PERSON><PERSON><PERSON>, RxCalendar, RxQuestionMark } from "react-icons/rx";
import { IoPersonAddOutline } from "react-icons/io5";
import EditLeadForm from "./EditLeadForm";
import LeadDetails from "./LeadDetails";
import AdditionalData from "./AdditionalData";
import { FaNoteSticky } from "react-icons/fa6";
import { useSelector } from "react-redux";
import { CgUserList } from "react-icons/cg";
import { Tooltip } from "react-tooltip";
import { MdOutlineContactPage } from "react-icons/md";
import { FaWpforms } from "react-icons/fa";
import QuotationsData from "./QuotationsData";
import classNames from "classnames";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import parseDateFn from "../../utils/parseDateFn";
import { handleResetLead } from "../../redux/features/clientSlice";
import { useDispatch } from "react-redux";
import { getAllLeadStatuses } from "../../config/leadStatusConfig";

const Overview = ({
  leadDetails,
  lastActivityDate,
  formattedCreatedAt,
  setLeadDetails,
}) => {
  const { currentUserPermissions } = useSelector((state) => state.auth);
  const { t } = useTranslation();
  const gridNumber = [
    formattedCreatedAt,
    leadDetails?.status,
    leadDetails?.created_by,
    lastActivityDate,
    leadDetails?.page_name,
    leadDetails?.form_name,
  ];
  const filteredGridNumber = gridNumber.filter(
    (item) => item !== undefined && item !== null
  );
  const sizeOfColumns = filteredGridNumber?.length === 6 ? 4 : 3;
  const columnsSizeLG = 4;
  const columnsSizeMD = 6;
  const [quotationsData, setQuotationsData] = useState([]);
  // Define constants for lead statuses to avoid hardcoding
  const LEAD_STATUS = {
    PENDING: 0,
    IN_PROGRESS: 1,
    COMPLETED: 2,
    REJECTED: 3,
    WRONG_LEAD: 4,
    NOT_QUALIFIED: 5,
    NO_COMMUNICATION: 6,
    QUOTATION: 10
  };

  // Get all possible lead statuses
  const allLeadStatuses = getAllLeadStatuses();
  
  // Create a mapping of status codes to translated status names
  const statusText = Object.entries(allLeadStatuses).reduce((acc, [code, name]) => {
    // Try to find a translation key for this status
    const translationKey = `status.${name.toLowerCase().replace(/\s+/g, '')}`;
    // Use the translation if available, otherwise use the name directly
    acc[code] = t(translationKey, name);
    return acc;
  }, {});

  const statusClass = classNames({
    "text-warning": leadDetails?.status === LEAD_STATUS.PENDING,
    "text-primary": [LEAD_STATUS.IN_PROGRESS, LEAD_STATUS.QUOTATION].includes(leadDetails?.status),
    "text-success": leadDetails?.status === LEAD_STATUS.COMPLETED,
    "text-danger": [LEAD_STATUS.REJECTED, LEAD_STATUS.WRONG_LEAD, LEAD_STATUS.NOT_QUALIFIED, LEAD_STATUS.NO_COMMUNICATION].includes(leadDetails?.status),
  });

  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  return (
    <>
      <Row className={"overview-details-row"}>
        <Col
          xl={sizeOfColumns}
          lg={columnsSizeLG}
          md={columnsSizeMD}
          className={"p-2"}
        >
          <div className={"overview-detail-card"}>
            <div>
              <RxCalendar size={24} className="mainColor" />
            </div>
            <div className={"fw-bold"}>{t("leadProfile.createdDate")}</div>
            <div className={""}>{formattedCreatedAt}</div>
          </div>
        </Col>
        <Col
          xl={sizeOfColumns}
          lg={columnsSizeLG}
          md={columnsSizeMD}
          className={"p-2"}
        >
          <div className={"overview-detail-card"}>
            <div>
              <CgUserList size={24} className="mainColor" />
            </div>
            <div className={"fw-bold"}>{t("leadProfile.leadStatus")}</div>
            <div className={statusClass}>{statusText[leadDetails?.status]}</div>
          </div>
        </Col>
        <Col
          xl={sizeOfColumns}
          lg={columnsSizeLG}
          md={columnsSizeMD}
          className={"p-2"}
        >
          <div className={"overview-detail-card"}>
            <div>
              <IoPersonAddOutline size={24} className="mainColor" />
            </div>
            <div className={"fw-bold"}>{t("leadProfile.createdBy")}</div>
            <div>{leadDetails?.created_by}</div>
          </div>
        </Col>
        {leadDetails?.page_name ? (
          <Col xl={sizeOfColumns} md={columnsSizeMD} className={"p-2"}>
            <div className={"overview-detail-card"}>
              <div>
                <MdOutlineContactPage size={24} className="mainColor" />
              </div>
              <div className={"fw-bold"}>Page name</div>
              <div className={"text-truncate one-line"} id={"page_name"}>
                {leadDetails?.page_name}
              </div>
              <Tooltip
                anchorSelect={"#page_name"}
                className={"bg-dark text-white"}
                content={leadDetails?.page_name}
              />
            </div>
          </Col>
        ) : null}
        {leadDetails?.form_name ? (
          <Col
            xl={sizeOfColumns}
            lg={columnsSizeLG}
            md={columnsSizeMD}
            className={"p-2"}
          >
            <div className={"overview-detail-card"}>
              <div>
                <FaWpforms size={24} className="mainColor" />
              </div>
              <div className={"fw-bold"}>Form name</div>
              <div className={"text-truncate one-line"} id={"form_name"}>
                {leadDetails?.form_name}
              </div>
              <Tooltip
                anchorSelect={"#form_name"}
                className={"bg-dark text-white"}
                content={leadDetails?.form_name}
              />
            </div>
          </Col>
        ) : null}
        <Col
          xl={sizeOfColumns}
          lg={columnsSizeLG}
          md={columnsSizeMD}
          className={"p-2"}
        >
          <div className={"overview-detail-card"}>
            <div>
              <RxActivityLog size={24} className="mainColor" />
            </div>
            <div className={"fw-bold"}>{t("leadProfile.latestActivity")}</div>
            <div>{parseDateFn(lastActivityDate)}</div>
          </div>
        </Col>
      </Row>
      <Row>
        <Col lg={4}>
          <div className={"about-contact"}>
            <div className={"fw-bold mainColor fs-6 mb-3"}>
              {t("leadProfile.aboutThisContact")}
            </div>
            {currentUserPermissions?.includes("lead-edit") ? (
              <EditLeadForm
                leadDetails={leadDetails}
                setLeadDetails={setLeadDetails}
              />
            ) : (
              <LeadDetails leadDetails={leadDetails} />
            )}
          </div>
        </Col>
        <Col lg={4} className={"d-flex flex-column"}>
          <AdditionalData leadDetails={leadDetails} />
          <QuotationsData
            leadDetails={leadDetails}
            quotationsData={quotationsData}
            setQuotationsData={setQuotationsData}
          />
        </Col>
        <Col lg={4}>
          <div className={"about-contact"}>
            <div
              className={
                "fw-bold mainColor fs-6 d-flex justify-content-between mb-3"
              }
            >
              <div>{t("leadProfile.notes")}</div>
              <div>
                <FaNoteSticky />
              </div>
            </div>
            <div
              className={"fs-6 opacity-50"}
              style={{ maxHeight: "450px", overflowY: "auto" }}
            >
              <ul>
                {leadDetails?.activities?.map((activity) =>
                  activity?.note ? (
                    <li
                      className={"text-start py-2"}
                      style={{ borderBottom: "1px solid rgb(204 205 206)" }}
                      key={activity?.id}
                    >
                      {activity?.note}
                    </li>
                  ) : (
                    <li
                      className={"text-start"}
                      style={{ borderBottom: "1px solid rgb(204 205 206)" }}
                      key={activity?.id}
                    >
                      {activity?.result}
                      <span>{activity?.next_date}</span>
                    </li>
                  )
                )}
              </ul>
            </div>
          </div>
        </Col>
      </Row>
      {user?.user?.role === 0 ? (
        <div className="w-100 d-flex justify-content-end align-content-end">
          <div className="d-flex align-items-center">
            <Button
              type="button"
              variant={"outline-danger"}
              id="reset-lead-info"
              className="rounded-3 py-2 fs-6 ms-3 d-flex align-items-center gap-2"
              onClick={() => {
                dispatch(handleResetLead(leadDetails?.id))
                  .unwrap()
                  .then((result) => {
                    if (result) {
                      setLeadDetails(result);
                    }
                  });
              }}
            >
              {t("leadProfile.resetLead")}
              <span className="ms-1">
                <RxQuestionMark size={16} />
              </span>
            </Button>
            <Tooltip
              anchorSelect="#reset-lead-info"
              className="bg-dark text-white"
              content={
                t("leadProfile.resetLeadTooltip") ||
                "Reset the lead to its initial state"
              }
            />
          </div>
        </div>
      ) : null}
    </>
  );
};

export default Overview;
